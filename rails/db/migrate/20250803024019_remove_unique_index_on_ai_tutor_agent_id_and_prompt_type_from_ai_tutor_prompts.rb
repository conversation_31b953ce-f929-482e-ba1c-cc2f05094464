class RemoveUniqueIndexOnAiTutorAgentIdAndPromptTypeFromAiTutorPrompts < ActiveRecord::Migration[7.0]
  # 20250803024019
  def up
    # Remove the unique index on ai_tutor_agent_id and prompt_type
    remove_index :ai_tutor_prompts, name: 'index_ai_tutor_prompts_on_ai_tutor_agent_id_and_prompt_type'
  end

  def down
    # Re-add the unique index if needed to rollback
    add_index :ai_tutor_prompts, [:ai_tutor_agent_id, :prompt_type],
              unique: true,
              name: 'index_ai_tutor_prompts_on_ai_tutor_agent_id_and_prompt_type'
  end
end
