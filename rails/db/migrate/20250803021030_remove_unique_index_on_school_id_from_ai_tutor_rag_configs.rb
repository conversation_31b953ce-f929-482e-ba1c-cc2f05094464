class RemoveUniqueIndexOnSchoolIdFromAiTutorRagConfigs < ActiveRecord::Migration[7.0]
  def up
    if foreign_key_exists?(:ai_tutor_rag_configs, :schools)
      remove_foreign_key :ai_tutor_rag_configs, :schools
    end

    if index_exists?(:ai_tutor_rag_configs, :school_id, unique: true)
      remove_index :ai_tutor_rag_configs, column: :school_id, unique: true
    end

    unless index_exists?(:ai_tutor_rag_configs, :school_id)
      add_index :ai_tutor_rag_configs, :school_id
    end

    unless foreign_key_exists?(:ai_tutor_rag_configs, :schools)
      add_foreign_key :ai_tutor_rag_configs, :schools
    end
  end

  def down
    if foreign_key_exists?(:ai_tutor_rag_configs, :schools)
      remove_foreign_key :ai_tutor_rag_configs, :schools
    end

    if index_exists?(:ai_tutor_rag_configs, :school_id) && !index_exists?(:ai_tutor_rag_configs, :school_id, unique: true)
      remove_index :ai_tutor_rag_configs, :school_id
    end

    unless index_exists?(:ai_tutor_rag_configs, :school_id, unique: true)
      add_index :ai_tutor_rag_configs, :school_id, unique: true
    end

    unless foreign_key_exists?(:ai_tutor_rag_configs, :schools)
      add_foreign_key :ai_tutor_rag_configs, :schools
    end
  end
end
