class FixUniqueIndexOnAiTutorAgentIdInRagConfigs < ActiveRecord::Migration[7.0]
  def up
    if foreign_key_exists?(:ai_tutor_rag_configs, :ai_tutor_agents)
      remove_foreign_key :ai_tutor_rag_configs, :ai_tutor_agents
    end

    if index_exists?(:ai_tutor_rag_configs, :ai_tutor_agent_id) && !index_exists?(:ai_tutor_rag_configs, :ai_tutor_agent_id, unique: true)
      remove_index :ai_tutor_rag_configs, :ai_tutor_agent_id
    end

    unless index_exists?(:ai_tutor_rag_configs, :ai_tutor_agent_id, unique: true)
      add_index :ai_tutor_rag_configs, :ai_tutor_agent_id, unique: true
    end

    unless foreign_key_exists?(:ai_tutor_rag_configs, :ai_tutor_agents)
      add_foreign_key :ai_tutor_rag_configs, :ai_tutor_agents
    end
  end

  def down
    if foreign_key_exists?(:ai_tutor_rag_configs, :ai_tutor_agents)
      remove_foreign_key :ai_tutor_rag_configs, :ai_tutor_agents
    end

    if index_exists?(:ai_tutor_rag_configs, :ai_tutor_agent_id, unique: true)
      remove_index :ai_tutor_rag_configs, column: :ai_tutor_agent_id, unique: true
    end

    unless index_exists?(:ai_tutor_rag_configs, :ai_tutor_agent_id)
      add_index :ai_tutor_rag_configs, :ai_tutor_agent_id
    end

    unless foreign_key_exists?(:ai_tutor_rag_configs, :ai_tutor_agents)
      add_foreign_key :ai_tutor_rag_configs, :ai_tutor_agents
    end
  end
end
