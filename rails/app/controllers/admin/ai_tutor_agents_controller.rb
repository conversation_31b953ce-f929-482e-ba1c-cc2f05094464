class Admin::Ai<PERSON>utorAgentsController < Admin::ApplicationController
  before_action :set_school
  before_action :set_ai_tutor_agent, only: [:show, :edit, :update, :destroy, :set_default]

  def index
    # Get only custom agents created by admin
    @ai_tutor_agents = @school.ai_tutor_agents
                              .where(agent_category: 'custom')
                              .order(:name)
  end

  def show
    # Redirect to prompts page instead of showing agent detail
    redirect_to admin_school_ai_tutor_agent_ai_tutor_prompts_path(@school, @ai_tutor_agent)
  end

  def new
    @ai_tutor_agent = @school.ai_tutor_agents.build
    @ai_tutor_agent.agent_category = 'custom'
  end

  def create
    @ai_tutor_agent = @school.ai_tutor_agents.build(ai_tutor_agent_params)

    if @ai_tutor_agent.save
      redirect_to admin_school_ai_tutor_agents_path(@school),
                  notice: 'AIチューターエージェントが正常に作成されました。'
    else
      render :new
    end
  end

  def edit
  end

  def update
    if @ai_tutor_agent.update(ai_tutor_agent_params)
      redirect_to admin_school_ai_tutor_agents_path(@school),
                  notice: 'AIチューターエージェントが正常に更新されました。'
    else
      render :edit
    end
  end

  def destroy
    if @ai_tutor_agent.ai_tutor_sources.present?
      redirect_to admin_school_ai_tutor_agents_path(@school),
                  alert: 'デフォルトエージェントは削除できません。'
      return
    end

    # Prevent deletion of system agents
    if @ai_tutor_agent.agent_category != 'custom'
      redirect_to admin_school_ai_tutor_agents_path(@school),
                  alert: 'システムエージェントは削除できません。'
      return
    end

    @ai_tutor_agent.destroy
    redirect_to admin_school_ai_tutor_agents_path(@school),
                notice: 'AIチューターエージェントが正常に削除されました。'
  end

  def set_default
    @ai_tutor_agent.transaction do
      @school.ai_tutor_agents.update_all(is_default: false)
      @ai_tutor_agent.update!(is_default: true)
    end

    redirect_to admin_school_ai_tutor_agents_path(@school),
                notice: 'デフォルトエージェントが設定されました。'
  rescue
    redirect_to admin_school_ai_tutor_agents_path(@school),
                alert: 'エラーが発生しました。'
  end

  private

  def set_school
    @school = School.find(params[:school_id])
  end

  def set_ai_tutor_agent
    @ai_tutor_agent = @school.ai_tutor_agents.find(params[:id])
  end

  def ai_tutor_agent_params
    params.require(:ai_tutor_agent).permit(:agent_type, :name, :description, :enabled,
                                           :image, :agent_category, :is_default)
  end
end
