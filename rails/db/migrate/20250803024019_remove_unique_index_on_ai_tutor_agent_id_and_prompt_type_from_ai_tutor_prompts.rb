class RemoveUniqueIndexOnAiTutorAgentIdAndPromptTypeFromAiTutorPrompts < ActiveRecord::Migration[7.0]
  def up
    remove_index :ai_tutor_prompts, name: 'index_ai_tutor_prompts_on_ai_tutor_agent_id_and_prompt_type'
  end

  def down
    add_index :ai_tutor_prompts, [:ai_tutor_agent_id, :prompt_type],
              unique: true,
              name: 'index_ai_tutor_prompts_on_ai_tutor_agent_id_and_prompt_type'
  end
end
