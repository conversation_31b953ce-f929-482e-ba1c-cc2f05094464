class RemoveUniqueIndexOnSchoolIdFromAiTutorRagConfigs < ActiveRecord::Migration[7.0]
  # 20250803021030
  def up
    # Remove the unique index on school_id since we now have unique index on ai_tutor_agent_id
    # This allows multiple rag configs per school (one per agent)

    # First, remove the foreign key constraint
    if foreign_key_exists?(:ai_tutor_rag_configs, :schools)
      remove_foreign_key :ai_tutor_rag_configs, :schools
    end

    # Remove the unique index
    if index_exists?(:ai_tutor_rag_configs, :school_id, unique: true)
      remove_index :ai_tutor_rag_configs, column: :school_id, unique: true
    end

    # Add a regular (non-unique) index on school_id for performance
    unless index_exists?(:ai_tutor_rag_configs, :school_id)
      add_index :ai_tutor_rag_configs, :school_id
    end

    # Re-add the foreign key constraint
    unless foreign_key_exists?(:ai_tutor_rag_configs, :schools)
      add_foreign_key :ai_tutor_rag_configs, :schools
    end
  end

  def down
    # Remove the foreign key constraint
    if foreign_key_exists?(:ai_tutor_rag_configs, :schools)
      remove_foreign_key :ai_tutor_rag_configs, :schools
    end

    # Remove the regular index
    if index_exists?(:ai_tutor_rag_configs, :school_id) && !index_exists?(:ai_tutor_rag_configs, :school_id, unique: true)
      remove_index :ai_tutor_rag_configs, :school_id
    end

    # Add back the unique index (this might fail if there are multiple configs per school)
    unless index_exists?(:ai_tutor_rag_configs, :school_id, unique: true)
      add_index :ai_tutor_rag_configs, :school_id, unique: true
    end

    # Re-add the foreign key constraint
    unless foreign_key_exists?(:ai_tutor_rag_configs, :schools)
      add_foreign_key :ai_tutor_rag_configs, :schools
    end
  end
end
