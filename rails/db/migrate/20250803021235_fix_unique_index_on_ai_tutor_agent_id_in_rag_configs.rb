class FixUniqueIndexOnAiTutorAgentIdInRagConfigs < ActiveRecord::Migration[7.0]
  # 20250803021235
  def up
    # Remove foreign key constraint first
    if foreign_key_exists?(:ai_tutor_rag_configs, :ai_tutor_agents)
      remove_foreign_key :ai_tutor_rag_configs, :ai_tutor_agents
    end

    # Remove existing non-unique index on ai_tutor_agent_id
    if index_exists?(:ai_tutor_rag_configs, :ai_tutor_agent_id) && !index_exists?(:ai_tutor_rag_configs, :ai_tutor_agent_id, unique: true)
      remove_index :ai_tutor_rag_configs, :ai_tutor_agent_id
    end

    # Add unique index on ai_tutor_agent_id (each agent should have only one rag config)
    unless index_exists?(:ai_tutor_rag_configs, :ai_tutor_agent_id, unique: true)
      add_index :ai_tutor_rag_configs, :ai_tutor_agent_id, unique: true
    end

    # Re-add foreign key constraint
    unless foreign_key_exists?(:ai_tutor_rag_configs, :ai_tutor_agents)
      add_foreign_key :ai_tutor_rag_configs, :ai_tutor_agents
    end
  end

  def down
    # Remove foreign key constraint first
    if foreign_key_exists?(:ai_tutor_rag_configs, :ai_tutor_agents)
      remove_foreign_key :ai_tutor_rag_configs, :ai_tutor_agents
    end

    # Remove unique index
    if index_exists?(:ai_tutor_rag_configs, :ai_tutor_agent_id, unique: true)
      remove_index :ai_tutor_rag_configs, column: :ai_tutor_agent_id, unique: true
    end

    # Add back regular index
    unless index_exists?(:ai_tutor_rag_configs, :ai_tutor_agent_id)
      add_index :ai_tutor_rag_configs, :ai_tutor_agent_id
    end

    # Re-add foreign key constraint
    unless foreign_key_exists?(:ai_tutor_rag_configs, :ai_tutor_agents)
      add_foreign_key :ai_tutor_rag_configs, :ai_tutor_agents
    end
  end
end
