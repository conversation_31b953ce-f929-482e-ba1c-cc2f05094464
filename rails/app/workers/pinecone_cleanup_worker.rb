class PineconeCleanupWorker
  include Sidekiq::Worker

  sidekiq_options queue: 'ai_training', retry: 3

  def perform(ai_tutor_agent_id, vector_ids, source_id = nil)
    return if vector_ids.blank?

    begin
      ai_tutor_agent = AiTutorAgent.find_by(id: ai_tutor_agent_id)

      return unless ai_tutor_agent

      Rails.logger.info "Cleaning up #{vector_ids.length} Pinecone vectors#{source_id ? " for source #{source_id}" : ""}"

      pinecone_service = PineconeService.for_agent(ai_tutor_agent)
      return unless pinecone_service.configured?

      # Delete vectors by IDs
      success = pinecone_service.delete_vectors(vector_ids)

      if success
        Rails.logger.info "Successfully cleaned up #{vector_ids.length} Pinecone vectors#{source_id ? " for source #{source_id}" : ""}"
      else
        Rails.logger.warn "Failed to cleanup Pinecone vectors#{source_id ? " for source #{source_id}" : ""}"
      end
    rescue => e
      Rails.logger.error "Error cleaning up Pinecone vectors: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      raise e # Let Sidekiq handle retry
    end
  end
end
