<% content_for :title, "新しいAIエージェントを作成 - #{@school.name}" %>

<%= render 'admin/shared/ai_tutor_styles' %>

<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h1 class="h3 mb-1">
            <i class="bi bi-plus-circle me-2"></i>
            新しいAIエージェントを作成
          </h1>
          <p class="text-muted mb-0"><%= @school.name %></p>
        </div>
        <%= link_to admin_school_ai_tutor_agents_path(@school), class: "btn btn-outline-secondary" do %>
          <i class="bi bi-arrow-left me-1"></i>戻る
        <% end %>
      </div>

      <div class="row justify-content-center">
        <div class="col-lg-8">
          <div class="card">
            <div class="card-header">
              <h4 class="card-title mb-0">
                <i class="bi bi-robot me-2"></i>
                エージェント情報
              </h4>
            </div>
            <div class="card-body">
              <%= form_with model: [@school, @ai_tutor_agent], url: admin_school_ai_tutor_agents_path(@school), local: true, html: { multipart: true, class: "needs-validation", novalidate: true } do |form| %>
                <% if @ai_tutor_agent.errors.any? %>
                  <div class="alert alert-danger">
                    <h6>エラーが発生しました:</h6>
                    <ul class="mb-0">
                      <% @ai_tutor_agent.errors.full_messages.each do |message| %>
                        <li><%= message %></li>
                      <% end %>
                    </ul>
                  </div>
                <% end %>

                <!-- Agent Image -->
                <div class="mb-4">
                  <label for="ai_tutor_agent_image" class="form-label">
                    <i class="bi bi-image me-1"></i>
                    エージェントアバター
                  </label>
                  <div class="d-flex align-items-start gap-3">
                    <div class="avatar-preview">
                      <div id="image-preview" class="border rounded-circle d-flex align-items-center justify-content-center" 
                           style="width: 80px; height: 80px; background-color: #f8f9fa;">
                        <i class="bi bi-person-circle text-muted" style="font-size: 3rem;"></i>
                      </div>
                    </div>
                    <div class="flex-grow-1">
                      <%= form.file_field :image, class: "form-control", accept: "image/*", id: "ai_tutor_agent_image" %>
                      <div class="form-text">
                        推奨サイズ: 200x200px以上、JPEGまたはPNG形式
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Agent Name -->
                <div class="mb-3">
                  <label for="ai_tutor_agent_name" class="form-label">
                    <i class="bi bi-person me-1"></i>
                    エージェント名 <span class="text-danger">*</span>
                  </label>
                  <%= form.text_field :name, class: "form-control", placeholder: "例: 数学の先生", required: true %>
                  <div class="invalid-feedback">
                    エージェント名を入力してください。
                  </div>
                </div>

                <!-- Agent Description -->
                <div class="mb-3">
                  <label for="ai_tutor_agent_description" class="form-label">
                    <i class="bi bi-card-text me-1"></i>
                    説明
                  </label>
                  <%= form.text_area :description, class: "form-control", rows: 4, 
                      placeholder: "このエージェントの役割や特徴を説明してください..." %>
                  <div class="form-text">
                    学習者がエージェントを選択する際の参考になる説明を入力してください。
                  </div>
                </div>

                <div class="mb-3">
                  <label for="ai_tutor_agent_agent_type" class="form-label">
                    <i class="bi bi-robot me-1"></i>
                    エージェントタイプ
                  </label>
                  <%= form.select :agent_type, AiTutorAgent::AGENT_TYPES.map { |type| [type.upcase, type] }, { selected: 'lesson' }, { class: "form-select" } %>
                </div>

                <%= form.hidden_field :agent_category, value: 'custom' %>

                <div class="mb-4">
                  <div class="form-check">
                    <%= form.check_box :enabled, class: "form-check-input", checked: true %>
                    <label class="form-check-label" for="ai_tutor_agent_enabled">
                      エージェントを有効にする
                    </label>
                  </div>
                  <div class="form-text">
                    無効にすると、学習者はこのエージェントを選択できなくなります。
                  </div>
                </div>

                <div class="d-flex gap-2">
                  <%= form.submit "エージェントを作成", class: "btn btn-success" %>
                  <%= link_to "キャンセル", admin_school_ai_tutor_agents_path(@school), class: "btn btn-outline-secondary" %>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Image preview functionality
  const imageInput = document.getElementById('ai_tutor_agent_image');
  const imagePreview = document.getElementById('image-preview');
  
  if (imageInput && imagePreview) {
    imageInput.addEventListener('change', function(e) {
      const file = e.target.files[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
          imagePreview.innerHTML = `<img src="${e.target.result}" class="rounded-circle" style="width: 80px; height: 80px; object-fit: cover;">`;
        };
        reader.readAsDataURL(file);
      }
    });
  }

  // Form validation
  const forms = document.querySelectorAll('.needs-validation');
  Array.from(forms).forEach(form => {
    form.addEventListener('submit', event => {
      if (!form.checkValidity()) {
        event.preventDefault();
        event.stopPropagation();
      }
      form.classList.add('was-validated');
    });
  });
});
</script>
